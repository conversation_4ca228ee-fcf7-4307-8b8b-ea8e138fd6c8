import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent } from './ui/card';
import { Calculator, Save, User, Building2, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getAllTenants,
  getTenant,
  createRentEntry,
  getRentEntriesByMonth,
  type Tenant,
  type RentEntry
} from '@/services/api';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export const RentEntryForm: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [isLoadingEntries, setIsLoadingEntries] = useState(false);

  const [formData, setFormData] = useState<Partial<RentEntry>>({
    rentMonth: new Date().toISOString().slice(0, 7), // Current month YYYY-MM
    building: '',
    floor: 'GF',
    rentDetails: {
      cashRent: 0,
      bankRent: 0,
      roomRent: 0,
      gst: 18, // Fixed GST at 18%
      gstAmount: 0,
      totalRent: 0,
      totalPayable: 0
    },
    paymentStatus: 'pending'
  });

  const buildings = ['Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const floors = ['GF', '1st', '2nd', '3rd', '4th', '5th'];

  useEffect(() => {
    fetchTenants();
  }, []);

  useEffect(() => {
    if (formData.rentMonth) {
      fetchRentEntriesByMonth(formData.rentMonth);
    }
  }, [formData.rentMonth]);

  const fetchTenants = async () => {
    try {
      setIsLoading(true);
      const response = await getAllTenants();
      setTenants(response.data || []);
    } catch (error: any) {
      console.error('Error fetching tenants:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch tenants",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRentEntriesByMonth = async (month: string) => {
    try {
      setIsLoadingEntries(true);
      console.log(`Fetching rent entries for month: ${month}`);
      const response = await getRentEntriesByMonth(month);
      console.log(`Received ${response.data?.length || 0} rent entries:`, response.data);
      setRentEntries(response.data || []);
    } catch (error: any) {
      console.error('Error fetching rent entries:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch rent entries",
        variant: "destructive",
      });
    } finally {
      setIsLoadingEntries(false);
    }
  };

  const calculateRent = (data: typeof formData) => {
    if (!data.rentDetails) return data;

    // Convert values to numbers and handle empty strings
    const cashRent = Number(data.rentDetails.cashRent) || 0;
    const bankRent = Number(data.rentDetails.bankRent) || 0;
    const roomRent = Number(data.rentDetails.roomRent) || 0;
    const gst = 18; // Fixed GST at 18%

    // Calculate GST amount based on bank rent
    const gstAmount = Math.round(bankRent * (gst / 100));

    // Calculate total rent (cash rent + bank rent only)
    const totalRent = cashRent + bankRent;

    // Calculate total payable (total rent + GST)
    const totalPayable = totalRent + gstAmount;

    return {
      ...data,
      rentDetails: {
        ...data.rentDetails,
        cashRent,
        bankRent,
        roomRent,
        gst,
        gstAmount,
        totalRent,
        totalPayable
      }
    };
  };

  const handleBuildingFloorChange = async (building: string, floor: string) => {
    // Find tenant for this building and floor
    const tenant = tenants.find(t => t.building === building && t.floor === floor);

    if (tenant) {
      setFormData(prev => ({
        ...prev,
        tenant: tenant.id,
        building,
        floor,
        rentDetails: {
          ...tenant.rentDetails,
          gst: tenant.rentDetails.gst || 18 // Use tenant's GST if available, otherwise default to 18
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        tenant: undefined,
        building,
        floor,
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        }
      }));
    }
  };

  const handleRentChange = (field: keyof typeof formData.rentDetails, value: number) => {
    const newFormData = {
      ...formData,
      rentDetails: {
        ...formData.rentDetails!,
        [field]: value
      }
    };

    const calculatedRent = calculateRent(newFormData);
    setFormData(calculatedRent);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.tenant) {
      toast({
        title: "Error",
        description: "No tenant found for this building and floor",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      // Format the rentMonth to include day (first day of the month)
      const rentEntryData = {
        ...formData,
        rentMonth: `${formData.rentMonth}-01`,
      } as RentEntry;

      const response = await createRentEntry(rentEntryData);

      if (!response.success) {
        toast({
          title: "Error",
          description: response.message || "Failed to save rent entry",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: `Rent entry for ${formData.rentMonth} saved successfully!`,
      });

      // Refresh rent entries for the current month
      if (formData.rentMonth) {
        fetchRentEntriesByMonth(formData.rentMonth);
      }

      // Reset form with initial values to maintain controlled inputs
      setFormData({
        rentMonth: new Date().toISOString().slice(0, 7),
        building: '',
        floor: 'GF',
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        },
        paymentStatus: 'pending'
      });
    } catch (error: any) {
      console.error('Error saving rent entry:', error);
      toast({
        title: "Error",
        description: error.response?.data?.message || error.message || "Failed to save rent entry",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTenantDetails = () => {
    if (!formData.tenant) return null;
    const tenant = tenants.find(t => t.id === formData.tenant);
    return tenant;
  };

  const isEligibleForRent = (tenant: any, selectedMonth: string) => {
    if (!tenant.rentStartDate) return false;

    // Convert dates to start of month for comparison
    const rentStartDate = new Date(tenant.rentStartDate);
    const rentStartMonth = new Date(rentStartDate.getFullYear(), rentStartDate.getMonth(), 1);

    const selectedDate = new Date(`${selectedMonth}-01`);
    const selectedMonthDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);

    // For debugging
    console.log('Checking eligibility for', tenant.fullName, {
      rentStartDate: rentStartMonth.toISOString(),
      selectedMonth: selectedMonthDate.toISOString(),
      isEligible: selectedMonthDate >= rentStartMonth
    });

    // Check if tenant is active and selected month is on or after rent start month
    return tenant.isActive !== false && selectedMonthDate >= rentStartMonth;
  };

  const getExpectedRentMonths = (tenant: any, selectedMonth: string) => {
    if (!tenant.rentStartDate) return [];

    const rentStartDate = new Date(tenant.rentStartDate);
    const rentStartMonth = new Date(rentStartDate.getFullYear(), rentStartDate.getMonth(), 1);

    const selectedDate = new Date(`${selectedMonth}-01`);
    const selectedMonthDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);

    // If selected month is before rent start, no rent is expected
    if (selectedMonthDate < rentStartMonth) {
      return [];
    }

    // If selected month is the rent start month or after, rent is expected
    return [selectedMonthDate];
  };

  const getTenantRentEntry = (tenantId: string, month?: string) => {
    const targetMonth = month || formData.rentMonth;
    if (!targetMonth) return null;

    console.log(`\n🔍 Looking for rent entry - Tenant ID: ${tenantId}, Month: ${targetMonth}`);
    console.log('Available rent entries:', rentEntries.length);

    const entry = rentEntries.find(entry => {
      const entryTenantId = typeof entry.tenant === 'string' ? entry.tenant : entry.tenant.id || (entry.tenant as any)._id;

      console.log('Checking entry:', {
        entryId: entry.id,
        entryTenantId,
        targetTenantId: tenantId,
        entryRentMonth: entry.rentMonth,
        targetMonth,
        paymentStatus: entry.paymentStatus
      });

      // Check if tenant ID matches
      if (entryTenantId !== tenantId) {
        console.log('❌ Tenant ID mismatch');
        return false;
      }

      // Parse entry date and target date more carefully
      const entryDate = new Date(entry.rentMonth);
      const entryYear = entryDate.getFullYear();
      const entryMonth = entryDate.getMonth() + 1;

      const [targetYear, targetMonthNum] = targetMonth.split('-').map(Number);

      // Compare year and month
      const isMatch = entryYear === targetYear && entryMonth === targetMonthNum;

      console.log('Date comparison:', {
        entryId: entry.id,
        entryDate: `${entryYear}-${String(entryMonth).padStart(2, '0')}`,
        targetDate: `${targetYear}-${String(targetMonthNum).padStart(2, '0')}`,
        isMatch,
        paymentStatus: entry.paymentStatus
      });

      return isMatch;
    });

    console.log(`Found entry:`, entry ? {
      id: entry.id,
      rentMonth: entry.rentMonth,
      paymentStatus: entry.paymentStatus,
      tenant: typeof entry.tenant === 'string' ? entry.tenant : entry.tenant.fullName
    } : 'No entry found');

    return entry || null;
  };

  const getTenantById = (tenantId: string) => {
    // First try to find by direct ID match
    let tenant = tenants.find(t => t.id === tenantId);

    // If not found, try matching against _id field
    if (!tenant) {
      tenant = tenants.find(t => (t as any)._id === tenantId);
    }

    return tenant;
  };

  const getPendingRents = () => {
    if (!formData.rentMonth) return [];

    console.log('=== DEBUGGING PENDING RENTS ===');
    console.log('Getting pending rents for month:', formData.rentMonth);

    // Get all tenants who should pay rent for the selected month
    const eligibleTenants = tenants.filter(tenant => {
      console.log(`\n--- Checking tenant: ${tenant.fullName} ---`);

      // Basic validation
      if (!tenant || !tenant.rentStartDate) {
        console.log('❌ Tenant invalid or no start date:', tenant?.fullName);
        return false;
      }

      // Only include active tenants
      if (tenant.isActive === false) {
        console.log('❌ Tenant is inactive:', tenant.fullName);
        return false;
      }

      // Parse dates for comparison - normalize to month level
      const rentStartDate = new Date(tenant.rentStartDate);
      const rentStartYear = rentStartDate.getFullYear();
      const rentStartMonth = rentStartDate.getMonth() + 1; // JavaScript months are 0-based

      const [selectedYear, selectedMonth] = formData.rentMonth!.split('-').map(Number);

      console.log('Date comparison:', {
        tenant: tenant.fullName,
        originalRentStartDate: tenant.rentStartDate,
        rentStartDate: `${rentStartYear}-${String(rentStartMonth).padStart(2, '0')}`,
        selectedDate: `${selectedYear}-${String(selectedMonth).padStart(2, '0')}`,
        rentStartYear,
        rentStartMonth,
        selectedYear,
        selectedMonth
      });

      // Compare year and month directly (not full dates)
      let isAfterOrEqualToStartDate = false;

      if (selectedYear > rentStartYear) {
        isAfterOrEqualToStartDate = true;
      } else if (selectedYear === rentStartYear) {
        isAfterOrEqualToStartDate = selectedMonth >= rentStartMonth;
      }

      console.log('Is after or equal to start date:', isAfterOrEqualToStartDate);

      // Check if rent is already paid for this month
      const rentEntry = getTenantRentEntry(tenant.id, formData.rentMonth!);
      console.log('Rent entry check:', {
        tenantId: tenant.id,
        hasEntry: !!rentEntry,
        entryStatus: rentEntry?.paymentStatus,
        building: tenant.building,
        floor: tenant.floor
      });

      // Building/floor filter if selected
      const buildingMatch = !formData.building || tenant.building === formData.building;
      const floorMatch = !formData.floor || tenant.floor === formData.floor;

      if (!buildingMatch) {
        console.log('❌ Building mismatch for tenant:', tenant.fullName, {
          expected: formData.building,
          actual: tenant.building
        });
        return false;
      }

      if (!floorMatch) {
        console.log('❌ Floor mismatch for tenant:', tenant.fullName, {
          expected: formData.floor,
          actual: tenant.floor
        });
        return false;
      }

      // Include tenant if:
      // 1. Selected month is equal to or after their rent start date AND
      // 2. No rent entry exists or rent entry exists but is not paid AND
      // 3. Building and floor match (if selected)
      const shouldInclude = isAfterOrEqualToStartDate &&
        (!rentEntry || rentEntry.paymentStatus !== 'paid') &&
        buildingMatch &&
        floorMatch;

      console.log('Final eligibility check:', {
        tenant: tenant.fullName,
        isAfterOrEqualToStartDate,
        hasRentEntry: !!rentEntry,
        rentEntryStatus: rentEntry?.paymentStatus,
        buildingMatch,
        floorMatch,
        shouldInclude
      });

      return shouldInclude;
    });

    console.log('Eligible tenants:', eligibleTenants.map(t => ({
      name: t.fullName,
      building: t.building,
      floor: t.floor,
      rentStartDate: t.rentStartDate
    })));

    // Sort tenants by building and floor
    const sortedTenants = eligibleTenants.sort((a, b) => {
      // First sort by building
      if (a.building < b.building) return -1;
      if (a.building > b.building) return 1;

      // Then sort by floor (custom order)
      const floorOrder = { 'GF': 0, '1st': 1, '2nd': 2, '3rd': 3, '4th': 4, '5th': 5 };
      return (floorOrder[a.floor] || 0) - (floorOrder[b.floor] || 0);
    });

    return sortedTenants.map(tenant => ({
      ...tenant,
      rentDetails: {
        cashRent: tenant.rentDetails?.cashRent || 0,
        bankRent: tenant.rentDetails?.bankRent || 0,
        roomRent: tenant.rentDetails?.roomRent || 0,
        gstAmount: tenant.rentDetails?.gstAmount || 0,
        totalRent: tenant.rentDetails?.totalRent || 0,
        totalPayable: tenant.rentDetails?.totalPayable || 0
      },
      paymentStatus: 'pending'
    }));
  };

  const getPaidRents = () => {
    if (!formData.rentMonth) return [];

    // First, get all entries for the selected month that are marked as paid
    const paidEntries = rentEntries.filter(entry => {
      // Must be a paid entry
      if (entry.paymentStatus !== 'paid') return false;

      // Check if entry matches selected month
      const entryDate = new Date(entry.rentMonth);
      const selectedDate = new Date(`${formData.rentMonth}-01`);

      // Only include entries for the current month
      return entryDate.getFullYear() === selectedDate.getFullYear() &&
        entryDate.getMonth() === selectedDate.getMonth();
    });

    // Now enrich the entries with tenant details
    return paidEntries.map(entry => {
      // Get tenant ID, handling both string and object cases
      const entryTenantId = typeof entry.tenant === 'string'
        ? entry.tenant
        : entry.tenant.id || (entry.tenant as any)._id;

      // Find the tenant
      const tenant = getTenantById(entryTenantId);

      // Create enriched entry
      return {
        ...entry,
        tenant: tenant ? {
          id: tenant.id || (tenant as any)._id,
          fullName: tenant.fullName,
          firmName: tenant.firmName,
          building: tenant.building,
          floor: tenant.floor
        } : {
          id: entryTenantId,
          fullName: typeof entry.tenant === 'object' ? entry.tenant.fullName : 'Unknown Tenant',
          firmName: typeof entry.tenant === 'object' ? entry.tenant.firmName : '',
          building: entry.building,
          floor: entry.floor
        },
        rentDetails: {
          cashRent: entry.rentDetails?.cashRent || 0,
          bankRent: entry.rentDetails?.bankRent || 0,
          roomRent: entry.rentDetails?.roomRent || 0,
          gstAmount: entry.rentDetails?.gstAmount || 0,
          totalRent: entry.rentDetails?.totalRent || 0,
          totalPayable: entry.rentDetails?.totalPayable || 0
        }
      };
    });
  };

  const pendingRents = getPendingRents();
  const paidRents = getPaidRents();

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Monthly Rent Entry</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Month Selection */}
              <div>
                <Label htmlFor="rentMonth">Rent Month *</Label>
                <Input
                  id="rentMonth"
                  type="month"
                  value={formData.rentMonth}
                  onChange={(e) => setFormData(prev => ({ ...prev, rentMonth: e.target.value }))}
                  required
                  className="h-10 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 [color-scheme:light] dark:[color-scheme:dark]"
                />
              </div>

              {/* Property Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="building">Building *</Label>
                  <Select value={formData.building} onValueChange={(value) => handleBuildingFloorChange(value, formData.floor!)}>
                    <SelectTrigger id="building" className="w-full">
                      <SelectValue placeholder="Select Building" />
                    </SelectTrigger>
                    <SelectContent>
                      {buildings.map(building => (
                        <SelectItem key={building} value={building}>{building}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="floor">Floor *</Label>
                  <Select value={formData.floor} onValueChange={(value) => handleBuildingFloorChange(formData.building!, value)}>
                    <SelectTrigger id="floor" className="w-full">
                      <SelectValue placeholder="Select Floor" />
                    </SelectTrigger>
                    <SelectContent>
                      {floors.map(floor => (
                        <SelectItem key={floor} value={floor}>{floor}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Auto-populated Tenant Information */}
              {formData.tenant && (
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg space-y-3">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Tenant Details</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Name:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.fullName || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Firm:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.firmName || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Phone:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.phone || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Email:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.email || '-'}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Rent Amount */}
              {formData.building && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calculator className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Rent Details - {formData.rentMonth}</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label htmlFor="cashRent" className="text-gray-700 dark:text-gray-300">Cash Rent (₹) *</Label>
                      <Input
                        id="cashRent"
                        type="number"
                        value={formData.rentDetails?.cashRent || ''}
                        onChange={(e) => handleRentChange('cashRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bankRent" className="text-gray-700 dark:text-gray-300">Bank Rent (₹) *</Label>
                      <Input
                        id="bankRent"
                        type="number"
                        value={formData.rentDetails?.bankRent || ''}
                        onChange={(e) => handleRentChange('bankRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                    <div>
                      <Label htmlFor="gst" className="text-gray-700 dark:text-gray-300">GST (%)</Label>
                      <Input
                        id="gst"
                        type="number"
                        value={18}
                        disabled
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 cursor-not-allowed bg-gray-100"
                      />
                    </div>
                    <div>
                      <Label htmlFor="roomRent" className="text-gray-700 dark:text-gray-300">Room Rent (₹)</Label>
                      <Input
                        id="roomRent"
                        type="number"
                        value={formData.rentDetails?.roomRent || ''}
                        onChange={(e) => handleRentChange('roomRent', Number(e.target.value))}
                        placeholder="0"
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                  </div>
                </div>
              )}

              {formData.building && (
                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3"
                  disabled={isLoading}
                >
                  <Save className="h-5 w-5 mr-2" />
                  {isLoading ? 'Saving...' : `Save Rent Entry for ${formData.rentMonth}`}
                </Button>
              )}
            </form>
          </Card>
        </div>

        {/* Preview Card */}
        <div className="lg:col-span-1">
          <Card className="sticky top-8 bg-white dark:bg-gray-800 border-0">
            <CardContent className="p-6 space-y-6">
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h3 className="font-medium text-gray-900 dark:text-gray-100">Rent Summary</h3>
              </div>

              {formData.building ? (
                <div className="space-y-6">
                  {/* Property Details */}
                  <div className="bg-blue-50/50 dark:bg-blue-900/10 rounded-lg p-4 space-y-1">
                    <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400 mb-2">
                      <Building2 className="h-4 w-4" />
                      <span className="font-medium">Property</span>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{formData.building} - {formData.floor}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Month: {formData.rentMonth}</p>
                  </div>

                  {/* Rent Breakdown */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Cash Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.cashRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Bank Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.bankRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">GST ({formData.rentDetails?.gst}%):</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.gstAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Room Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.roomRent.toLocaleString()}</span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900 dark:text-gray-100">Total Rent:</span>
                        <span className="font-bold text-blue-600 dark:text-blue-400">₹{formData.rentDetails?.totalRent.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Total Payable (Inc. GST):</span>
                        <span className="font-medium text-green-600 dark:text-green-400">₹{formData.rentDetails?.totalPayable.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Tenant Details */}
                  {getTenantDetails() && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3 border border-gray-100 dark:border-gray-700">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Tenant</h4>
                      <div className="space-y-2">
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Name:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.fullName}</p>
                        </div>
                        {getTenantDetails()?.firmName && (
                          <div className="space-y-1">
                            <p className="text-sm text-gray-500 dark:text-gray-400">Firm:</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.firmName}</p>
                          </div>
                        )}
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Phone:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.phone}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Email:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.email || '-'}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building2 className="h-8 w-8 mx-auto mb-3 text-gray-400 dark:text-gray-600" />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    Select building and floor to view rent details
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Rent Lists Section */}
      {formData.rentMonth && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Rent Status for {new Date(formData.rentMonth + '-01').toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </h2>
            {isLoadingEntries && (
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                Loading...
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Pending Collections */}
            <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Pending Collections</h3>

                <div className="space-y-4">
                  {pendingRents.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">No pending collections for this month</p>
                    </div>
                  ) : (
                    <>
                      {pendingRents.map((tenant) => (
                        <div key={tenant.id} className="p-4 bg-gray-50/50 dark:bg-gray-800/50 rounded-lg border border-gray-200/50 dark:border-gray-700/50 hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                                <User className="h-5 w-5 text-red-600 dark:text-red-400" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">{tenant.fullName}</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1 mt-0.5">
                                  <Building2 className="h-3.5 w-3.5" />
                                  {tenant.building} - {tenant.floor}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-red-600 dark:text-red-400 font-semibold text-sm">₹{tenant.rentDetails.cashRent.toLocaleString()} Cash</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{tenant.rentDetails.bankRent.toLocaleString()} Bank</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{tenant.rentDetails.roomRent.toLocaleString()} Room</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{tenant.rentDetails.gstAmount.toLocaleString()} GST</div>
                            </div>
                          </div>
                          <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                            <div className="flex justify-between items-center text-xs">
                              <span className="text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700/50 px-2 py-1 rounded">
                                {(() => {
                                  const rentEntry = getTenantRentEntry(tenant.id);
                                  if (!rentEntry) return 'Not paid';
                                  if (rentEntry.paymentStatus === 'partial') return 'Partial payment';
                                  if (rentEntry.paymentStatus === 'pending') return 'Payment pending';
                                  return 'Pending';
                                })()}
                              </span>
                              <span className="text-gray-600 dark:text-gray-400">
                                Due: ₹{tenant.rentDetails.totalPayable.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}

                      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Pending Cash Rent:</span>
                            <span className="font-semibold text-red-600 dark:text-red-400">
                              ₹{pendingRents.reduce((sum, t) => sum + t.rentDetails.cashRent, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Pending Bank Rent:</span>
                            <span className="font-semibold text-blue-600 dark:text-blue-400">
                              ₹{pendingRents.reduce((sum, t) => sum + t.rentDetails.bankRent, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Pending Room Rent:</span>
                            <span className="font-semibold text-purple-600 dark:text-purple-400">
                              ₹{pendingRents.reduce((sum, t) => sum + t.rentDetails.roomRent, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Pending GST:</span>
                            <span className="font-semibold text-orange-600 dark:text-orange-400">
                              ₹{pendingRents.reduce((sum, t) => sum + t.rentDetails.gstAmount, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-base font-bold pt-3 mt-1 border-t border-gray-200 dark:border-gray-700">
                            <span className="text-gray-900 dark:text-gray-100">Total Pending:</span>
                            <span className="text-red-600 dark:text-red-400">
                              ₹{pendingRents.reduce((sum, t) => sum + t.rentDetails.totalPayable, 0).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Paid Collections */}
            <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Paid Collections</h3>

                <div className="space-y-4">
                  {paidRents.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">No paid collections for this month</p>
                    </div>
                  ) : (
                    <>
                      {paidRents.map((entry) => (
                        <div key={entry.id} className="p-4 bg-gray-50/50 dark:bg-gray-800/50 rounded-lg border border-gray-200/50 dark:border-gray-700/50 hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                                <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                                  {entry.tenant.fullName}
                                  {entry.tenant.firmName && (
                                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                                      ({entry.tenant.firmName})
                                    </span>
                                  )}
                                </div>
                                <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1 mt-0.5">
                                  <Building2 className="h-3.5 w-3.5" />
                                  {entry.building} - {entry.floor}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-green-600 dark:text-green-400 font-semibold text-sm">₹{entry.rentDetails.cashRent.toLocaleString()} Cash</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{entry.rentDetails.bankRent.toLocaleString()} Bank</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{entry.rentDetails.roomRent.toLocaleString()} Room</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{entry.rentDetails.gstAmount.toLocaleString()} GST</div>
                            </div>
                          </div>
                          <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                            <div className="flex justify-between items-center text-xs">
                              <span className="text-gray-600 dark:text-gray-400 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded">
                                Paid {entry.updatedAt ? `on ${new Date(entry.updatedAt).toLocaleDateString()}` : ''}
                              </span>
                              <span className="text-gray-600 dark:text-gray-400">
                                Total: ₹{entry.rentDetails.totalPayable.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}

                      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Collected Cash Rent:</span>
                            <span className="font-semibold text-green-600 dark:text-green-400">
                              ₹{paidRents.reduce((sum, e) => sum + e.rentDetails.cashRent, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Collected Bank Rent:</span>
                            <span className="font-semibold text-blue-600 dark:text-blue-400">
                              ₹{paidRents.reduce((sum, e) => sum + e.rentDetails.bankRent, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Collected Room Rent:</span>
                            <span className="font-semibold text-purple-600 dark:text-purple-400">
                              ₹{paidRents.reduce((sum, e) => sum + e.rentDetails.roomRent, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Total Collected GST:</span>
                            <span className="font-semibold text-orange-600 dark:text-orange-400">
                              ₹{paidRents.reduce((sum, e) => sum + e.rentDetails.gstAmount, 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between text-base font-bold pt-3 mt-1 border-t border-gray-200 dark:border-gray-700">
                            <span className="text-gray-900 dark:text-gray-100">Total Collected:</span>
                            <span className="text-green-600 dark:text-green-400">
                              ₹{paidRents.reduce((sum, e) => sum + e.rentDetails.totalPayable, 0).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};
