const mongoose = require('mongoose');

const rentEntrySchema = new mongoose.Schema({
    tenant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Tenant',
        required: [true, 'Please provide tenant ID']
    },
    rentMonth: {
        type: Date,
        required: [true, 'Please provide rent month']
    },
    building: {
        type: String,
        required: [true, 'Please provide building name']
    },
    floor: {
        type: String,
        required: [true, 'Please provide floor']
    },
    rentDetails: {
        cashRent: {
            type: Number,
            required: [true, 'Please provide cash rent amount']
        },
        bankRent: {
            type: Number,
            default: 0
        },
        roomRent: {
            type: Number,
            default: 0
        },
        gst: {
            type: Number,
            default: 18
        },
        gstAmount: {
            type: Number,
            default: 0
        },
        totalRent: {
            type: Number,
            required: true
        },
        totalPayable: {
            type: Number,
            required: true
        }
    },
    paymentStatus: {
        type: String,
        enum: ['pending', 'partial', 'paid'],
        default: 'pending'
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update timestamps on save
rentEntrySchema.pre('save', function (next) {
    this.updatedAt = Date.now();
    next();
});

module.exports = mongoose.model('RentEntry', rentEntrySchema); 